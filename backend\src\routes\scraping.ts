import { Router } from 'express';
import {
  scrapeProvider,
  scrapeAllProviders,
  getScrapingJob,
  getRecentScrapingJobs,
  getSupportedProviders,
  scheduledScraping,
} from '@/controllers/scrapingController';
import { authenticate, authorize } from '@/middleware/auth';
// Removed enum imports - using string literals instead
import { validate } from '@/utils/validation';
import { scrapingJobSchema } from '@/utils/validation';

const router = Router();

// Public routes
router.get('/providers', getSupportedProviders);

// Protected routes - Admin only
router.use(authenticate);
router.use(authorize('ADMIN', 'SUPER_ADMIN'));

router.post('/scrape', validate(scrapingJobSchema), scrapeProvider);
router.post('/scrape-all', scrapeAllProviders);
router.get('/jobs', getRecentScrapingJobs);
router.get('/jobs/:jobId', getScrapingJob);
router.post('/schedule', scheduledScraping);

export default router;
