# Deploy Backend to Vercel <PERSON>t (PowerShell)

Write-Host "🚀 Deploying 1300blk Backend to Vercel..." -ForegroundColor Green

# Check if vercel CLI is installed
try {
    vercel --version | Out-Null
} catch {
    Write-Host "❌ Vercel CLI not found. Installing..." -ForegroundColor Red
    npm install -g vercel
}

# Navigate to backend directory
Set-Location backend

Write-Host "📦 Building project..." -ForegroundColor Yellow
npm run build

Write-Host "🔧 Generating Prisma client..." -ForegroundColor Yellow
npx prisma generate

Write-Host "🌐 Deploying to Vercel..." -ForegroundColor Yellow
vercel --prod

Write-Host "✅ Deployment complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Copy your Vercel backend URL"
Write-Host "2. Update .env.production with: VITE_API_BASE_URL=https://your-backend-url.vercel.app/api"
Write-Host "3. Set up environment variables in Vercel dashboard"
Write-Host "4. Test your deployment: https://your-backend-url.vercel.app/health"
Write-Host ""
Write-Host "🔗 Environment variables to set in Vercel:" -ForegroundColor Cyan
Write-Host "   - DATABASE_URL"
Write-Host "   - JWT_SECRET"
Write-Host "   - NODE_ENV=production"
Write-Host "   - CORS_ORIGIN=https://1300blk.online"
