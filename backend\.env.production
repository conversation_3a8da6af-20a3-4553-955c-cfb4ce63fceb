# Production Environment Variables for Vercel
# Copy these to your Vercel environment variables

# Database (Use PostgreSQL or other cloud database for production)
DATABASE_URL="your-production-database-url"

# JWT
JWT_SECRET="your-production-jwt-secret-key-make-it-very-secure"
JWT_EXPIRES_IN="7d"

# Server
PORT=3000
NODE_ENV="production"

# API Keys for Sports Data
ESPN_API_KEY=""
ODDS_API_KEY=""
SPORTS_REFERENCE_API_KEY=""
SPORTS_RADAR_API_KEY=""
RAPID_API_KEY=""

# Scraping
PROXY_URL=""
SCRAPERAPI_KEY=""
REQUEST_TIMEOUT_SECONDS=20

# ML Model Configuration
MODEL_DIR="./models"
PREDICTION_CONFIDENCE_THRESHOLD=0.65

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# CORS - Allow multiple origins for production
CORS_ORIGIN="https://1300blk.online,https://www.1300blk.online"

# Cache (Use Redis Cloud or similar for production)
REDIS_URL="your-production-redis-url"
CACHE_TTL=3600
