{"name": "sports-prediction-backend", "version": "1.0.0", "description": "Sports prediction backend with Express, TypeScript, and Prisma", "main": "dist/server.js", "scripts": {"dev": "ts-node -r tsconfig-paths/register src/server.ts", "dev:watch": "nodemon --exec \"ts-node -r tsconfig-paths/register\" src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "tsx prisma/seed.ts", "setup": "node scripts/setup-database.js", "db:reset": "node scripts/reset-database.js", "vercel-build": "prisma generate && tsc", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "^5.20.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.4.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^3.0.3", "puppeteer": "^23.5.0", "winston": "^3.14.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/morgan": "^1.9.9", "@types/node": "^22.16.5", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.1.7", "prisma": "^5.20.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.1", "typescript": "^5.8.3"}, "keywords": ["sports", "prediction", "api", "express", "typescript", "prisma"], "author": "", "license": "MIT"}