import { ESPNScraper } from './espnScraper';
import { OddsAPIScraper } from './oddsApiScraper';
import { BaseScraper, ScrapingResult, MatchData } from './baseScraper';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
// Removed enum imports - using string literals instead

export interface ScrapingJobResult {
  jobId: string;
  success: boolean;
  totalMatches: number;
  savedMatches: number;
  errors: string[];
  results: ScrapingResult[];
}

export class ScrapingManager {
  private scrapers: Map<string, BaseScraper>;

  constructor() {
    this.scrapers = new Map();
    this.initializeScrapers();
  }

  private initializeScrapers(): void {
    this.scrapers.set('espn', new ESPNScraper());
    this.scrapers.set('odds-api', new OddsAPIScraper());
    // Add more scrapers here as needed
  }

  async scrapeProvider(provider: string, sport: string, league?: string): Promise<ScrapingResult> {
    const scraper = this.scrapers.get(provider.toLowerCase());
    
    if (!scraper) {
      return {
        success: false,
        error: `Unknown provider: ${provider}`,
        source: provider,
        timestamp: new Date(),
        matchesFound: 0,
      };
    }

    return await scraper.scrapeMatches(sport, league);
  }

  async scrapeAllProviders(sport: string, league?: string): Promise<ScrapingJobResult> {
    const jobId = await this.createScrapingJob('all', sport, league);
    const results: ScrapingResult[] = [];
    const errors: string[] = [];
    let totalMatches = 0;
    let savedMatches = 0;

    try {
      await this.updateScrapingJob(jobId, 'RUNNING');

      // Scrape from all available providers
      const scrapingPromises = Array.from(this.scrapers.entries()).map(async ([providerName, scraper]) => {
        try {
          logger.info(`Starting scraping from ${providerName} for ${sport}/${league}`);
          const result = await scraper.scrapeMatches(sport, league);
          results.push(result);
          
          if (result.success && result.data) {
            totalMatches += result.data.length;
            
            // Save matches to database
            const saved = await this.saveMatches(result.data);
            savedMatches += saved;
          }
          
          return result;
        } catch (error) {
          const errorMessage = `${providerName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          logger.error(`Scraping failed for ${providerName}: ${errorMessage}`);
          
          return {
            success: false,
            error: errorMessage,
            source: providerName,
            timestamp: new Date(),
            matchesFound: 0,
          };
        }
      });

      await Promise.all(scrapingPromises);

      const success = results.some(r => r.success) && errors.length === 0;
      
      await this.updateScrapingJob(jobId, success ? 'COMPLETED' : 'FAILED', {
        totalMatches,
        savedMatches,
        errors,
        results: results.map(r => ({
          source: r.source,
          success: r.success,
          matchesFound: r.matchesFound,
          error: r.error,
        })),
      });

      return {
        jobId,
        success,
        totalMatches,
        savedMatches,
        errors,
        results,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      
      await this.updateScrapingJob(jobId, 'FAILED', {
        error: errorMessage,
        errors,
      });

      return {
        jobId,
        success: false,
        totalMatches: 0,
        savedMatches: 0,
        errors,
        results,
      };
    }
  }

  private async saveMatches(matches: MatchData[]): Promise<number> {
    let savedCount = 0;

    for (const match of matches) {
      try {
        // Check if match already exists
        if (match.matchId && match.source) {
          const existing = await prisma.sportsData.findFirst({
            where: {
              externalId: match.externalId,
              source: match.source,
            },
          });

          if (existing) {
            // Update existing match
            await prisma.sportsData.update({
              where: { id: existing.id },
              data: {
                odds: match.odds ? JSON.stringify(match.odds) : null,
                stats: match.stats ? JSON.stringify(match.stats) : null,
                status: match.status,
                homeScore: match.homeScore,
                awayScore: match.awayScore,
              },
            });
          } else {
            // Create new match
            await prisma.sportsData.create({
              data: {
                ...match,
                odds: match.odds ? JSON.stringify(match.odds) : null,
                stats: match.stats ? JSON.stringify(match.stats) : null,
              },
            });
          }
        } else {
          // Create without duplicate check
          await prisma.sportsData.create({
            data: {
              ...match,
              odds: match.odds ? JSON.stringify(match.odds) : null,
              stats: match.stats ? JSON.stringify(match.stats) : null,
            },
          });
        }
        
        savedCount++;
      } catch (error) {
        logger.error(`Failed to save match: ${error}`);
      }
    }

    return savedCount;
  }

  private async createScrapingJob(provider: string, sport: string, league?: string): Promise<string> {
    const job = await prisma.scrapingJob.create({
      data: {
        provider,
        sport,
        league,
        status: 'PENDING',
      },
    });

    return job.id;
  }

  private async updateScrapingJob(
    jobId: string,
    status: string,
    results?: any
  ): Promise<void> {
    const updateData: any = { status };

    if (status === 'RUNNING') {
      updateData.startedAt = new Date();
    } else if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date();
    }

    if (results) {
      updateData.results = results;
    }

    await prisma.scrapingJob.update({
      where: { id: jobId },
      data: updateData,
    });
  }

  async getScrapingJob(jobId: string) {
    return await prisma.scrapingJob.findUnique({
      where: { id: jobId },
    });
  }

  async getRecentScrapingJobs(limit: number = 10) {
    return await prisma.scrapingJob.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
    });
  }

  getSupportedProviders(): string[] {
    return Array.from(this.scrapers.keys());
  }

  getSupportedSports(): string[] {
    return ['football', 'basketball', 'baseball', 'hockey', 'soccer'];
  }

  getSupportedLeagues(sport: string): string[] {
    const leagues: { [key: string]: string[] } = {
      'football': ['nfl', 'ncaaf'],
      'basketball': ['nba', 'ncaab'],
      'baseball': ['mlb'],
      'hockey': ['nhl'],
      'soccer': ['mls', 'epl', 'laliga', 'bundesliga'],
    };

    return leagues[sport.toLowerCase()] || [];
  }
}
