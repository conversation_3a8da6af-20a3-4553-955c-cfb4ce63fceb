import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DollarSign, Eye, EyeOff, Plus, Edit, Trash2, <PERSON>fresh<PERSON>w, Bar<PERSON>hart3, Target } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService as api } from '@/services/api';
import { Header } from '@/components/Header';

interface AdUnit {
  id: string;
  name: string;
  type: 'BANNER' | 'SIDEBAR' | 'INLINE' | 'POPUP' | 'VIDEO';
  position: 'HEADER' | 'SIDEBAR' | 'CONTENT' | 'FOOTER';
  status: 'ACTIVE' | 'PAUSED' | 'DRAFT';
  impressions: number;
  clicks: number;
  revenue: number;
  ctr: number;
  createdAt: string;
  updatedAt: string;
}

const AdminAds: React.FC = () => {
  const { toast } = useToast();
  const [adUnits, setAdUnits] = useState<AdUnit[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedAd, setSelectedAd] = useState<AdUnit | null>(null);

  const fetchAdUnits = async () => {
    try {
      setLoading(true);
      const response = await api.getAdUnits();
      if (response.success) {
        setAdUnits(response.data || []);
      } else {
        // Mock data for development
        setAdUnits([
          {
            id: '1',
            name: 'Header Banner',
            type: 'BANNER',
            position: 'HEADER',
            status: 'ACTIVE',
            impressions: 15420,
            clicks: 234,
            revenue: 156.78,
            ctr: 1.52,
            createdAt: '2024-01-01',
            updatedAt: '2024-01-15'
          },
          {
            id: '2',
            name: 'Sidebar Ad',
            type: 'SIDEBAR',
            position: 'SIDEBAR',
            status: 'ACTIVE',
            impressions: 8930,
            clicks: 145,
            revenue: 89.45,
            ctr: 1.62,
            createdAt: '2024-01-05',
            updatedAt: '2024-01-20'
          },
          {
            id: '3',
            name: 'Content Inline',
            type: 'INLINE',
            position: 'CONTENT',
            status: 'PAUSED',
            impressions: 5670,
            clicks: 67,
            revenue: 34.20,
            ctr: 1.18,
            createdAt: '2024-01-10',
            updatedAt: '2024-01-25'
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching ad units:', error);
      toast({
        title: "Error",
        description: "Failed to fetch ad units",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdUnits();
  }, []);

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      PAUSED: 'secondary',
      DRAFT: 'outline'
    };
    return <Badge variant={variants[status as keyof typeof variants] as any}>{status}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      BANNER: 'default',
      SIDEBAR: 'secondary',
      INLINE: 'outline',
      POPUP: 'destructive',
      VIDEO: 'default'
    };
    return <Badge variant={variants[type as keyof typeof variants] as any}>{type}</Badge>;
  };

  const handleToggleStatus = async (adId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'ACTIVE' ? 'PAUSED' : 'ACTIVE';
      const response = await api.updateAdStatus(adId, newStatus);
      if (response.success) {
        toast({
          title: "Success",
          description: `Ad ${newStatus.toLowerCase()} successfully`,
        });
        fetchAdUnits();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update ad status",
        variant: "destructive",
      });
    }
  };

  const handleDeleteAd = async (adId: string) => {
    try {
      const response = await api.deleteAdUnit(adId);
      if (response.success) {
        toast({
          title: "Success",
          description: "Ad unit deleted successfully",
        });
        fetchAdUnits();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete ad unit",
        variant: "destructive",
      });
    }
  };

  const stats = {
    totalRevenue: adUnits.reduce((sum, ad) => sum + ad.revenue, 0),
    totalImpressions: adUnits.reduce((sum, ad) => sum + ad.impressions, 0),
    totalClicks: adUnits.reduce((sum, ad) => sum + ad.clicks, 0),
    avgCTR: adUnits.length > 0 ? adUnits.reduce((sum, ad) => sum + ad.ctr, 0) / adUnits.length : 0,
    activeAds: adUnits.filter(ad => ad.status === 'ACTIVE').length
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Ad Management</h1>
          <p className="text-muted-foreground">Manage advertising units and revenue</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${stats.totalRevenue.toFixed(2)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalImpressions.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clicks</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClicks.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg CTR</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.avgCTR.toFixed(2)}%</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Ads</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeAds}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="units" className="space-y-6">
          <TabsList>
            <TabsTrigger value="units">Ad Units</TabsTrigger>
            <TabsTrigger value="settings">AdSense Settings</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="units">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Ad Units</CardTitle>
                    <CardDescription>Manage your advertising units</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button onClick={fetchAdUnits} variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Ad Unit
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create New Ad Unit</DialogTitle>
                          <DialogDescription>Add a new advertising unit</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="adName">Ad Name</Label>
                            <Input id="adName" placeholder="Header Banner" />
                          </div>
                          <div>
                            <Label htmlFor="adType">Ad Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="BANNER">Banner</SelectItem>
                                <SelectItem value="SIDEBAR">Sidebar</SelectItem>
                                <SelectItem value="INLINE">Inline</SelectItem>
                                <SelectItem value="POPUP">Popup</SelectItem>
                                <SelectItem value="VIDEO">Video</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="adPosition">Position</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select position" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="HEADER">Header</SelectItem>
                                <SelectItem value="SIDEBAR">Sidebar</SelectItem>
                                <SelectItem value="CONTENT">Content</SelectItem>
                                <SelectItem value="FOOTER">Footer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="adCode">Ad Code</Label>
                            <Textarea id="adCode" placeholder="Paste your ad code here..." />
                          </div>
                          <Button className="w-full">Create Ad Unit</Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Impressions</TableHead>
                      <TableHead>Clicks</TableHead>
                      <TableHead>CTR</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8">
                          Loading ad units...
                        </TableCell>
                      </TableRow>
                    ) : adUnits.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8">
                          No ad units found
                        </TableCell>
                      </TableRow>
                    ) : (
                      adUnits.map((ad) => (
                        <TableRow key={ad.id}>
                          <TableCell className="font-medium">{ad.name}</TableCell>
                          <TableCell>{getTypeBadge(ad.type)}</TableCell>
                          <TableCell>{ad.position}</TableCell>
                          <TableCell>{getStatusBadge(ad.status)}</TableCell>
                          <TableCell>{ad.impressions.toLocaleString()}</TableCell>
                          <TableCell>{ad.clicks.toLocaleString()}</TableCell>
                          <TableCell>{ad.ctr.toFixed(2)}%</TableCell>
                          <TableCell>${ad.revenue.toFixed(2)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleToggleStatus(ad.id, ad.status)}
                              >
                                {ad.status === 'ACTIVE' ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleDeleteAd(ad.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>AdSense Settings</CardTitle>
                <CardDescription>Configure your Google AdSense integration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="adsenseId">AdSense Publisher ID</Label>
                  <Input id="adsenseId" placeholder="ca-pub-xxxxxxxxxxxxxxxx" />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="autoAds" />
                  <Label htmlFor="autoAds">Enable Auto Ads</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="personalizedAds" />
                  <Label htmlFor="personalizedAds">Enable Personalized Ads</Label>
                </div>
                <Button>Save Settings</Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Ad Analytics</CardTitle>
                <CardDescription>Detailed performance metrics for your ads</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Analytics dashboard coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminAds;
