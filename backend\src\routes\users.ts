import { Router } from 'express';
import { getAllUsers, getUserById, updateUser, deleteUser, getUserStats } from '@/controllers/userController';
import { authenticate, authorize } from '@/middleware/auth';
// Removed enum imports - using string literals instead
import { validateQuery } from '@/utils/validation';
import { paginationSchema } from '@/utils/validation';

const router = Router();

// All routes require authentication
router.use(authenticate);

// Admin only routes
router.get('/', authorize('ADMIN', 'SUPER_ADMIN'), validateQuery(paginationSchema), getAllUsers);
router.get('/stats', authorize('ADMIN', 'SUPER_ADMIN'), getUserStats);
router.get('/:id', authorize('ADMIN', 'SUPER_ADMIN'), getUserById);
router.put('/:id', authorize('ADMIN', 'SUPER_ADMIN'), updateUser);
router.delete('/:id', authorize('SUPER_ADMIN'), deleteUser);

export default router;
