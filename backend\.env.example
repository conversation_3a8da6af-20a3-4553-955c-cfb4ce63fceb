# Database
DATABASE_URL="postgresql://username:password@localhost:5432/sports_prediction_db"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"

# API Keys for Sports Data
ESPN_API_KEY=""
ODDS_API_KEY=""
SPORTS_REFERENCE_API_KEY=""
SPORTS_RADAR_API_KEY=""
RAPID_API_KEY=""

# Scraping
PROXY_URL=""
SCRAPERAPI_KEY=""
REQUEST_TIMEOUT_SECONDS=20

# ML Model Configuration
MODEL_DIR="./models"
PREDICTION_CONFIDENCE_THRESHOLD=0.65

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# CORS
CORS_ORIGIN="http://localhost:8080"

# Cache
REDIS_URL="redis://localhost:6379"
CACHE_TTL=3600
