import { Request, Response, NextFunction } from 'express';
import { prisma } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
import { PredictionEngine } from '@/services/ml/predictionEngine';
import { logger } from '@/utils/logger';
// Removed enum imports - using string literals instead

const predictionEngine = new PredictionEngine();

export const getAllPredictions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Emergency CORS headers
    res.setHeader('Access-Control-Allow-Origin', 'https://www.1300blk.online');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    const {
      page = 1,
      limit = 20,
      sport,
      league,
      status,
      isPremium,
      minConfidence,
      startDate,
      endDate
    } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const where: any = {};
    if (sport) where.sport = sport;
    if (league) where.league = league;
    if (status) where.status = status;
    if (isPremium !== undefined) where.isPremium = isPremium === 'true';
    if (minConfidence) where.confidence = { gte: Number(minConfidence) };
    
    if (startDate || endDate) {
      where.matchDate = {};
      if (startDate) where.matchDate.gte = new Date(startDate as string);
      if (endDate) where.matchDate.lte = new Date(endDate as string);
    }

    // Only show published predictions to non-admin users
    const user = (req as AuthenticatedRequest).user;
    if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN')) {
      where.isPublished = true;
    }

    let predictions, total;

    try {
      [predictions, total] = await Promise.all([
        prisma.prediction.findMany({
          where,
          skip,
          take: Number(limit),
          orderBy: { matchDate: 'asc' },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
              },
            },
            sportsData: {
              select: {
                id: true,
                status: true,
                homeScore: true,
                awayScore: true,
              },
            },
          },
        }),
        prisma.prediction.count({ where }),
      ]);
    } catch (dbError) {
      logger.error('Database error in getAllPredictions:', dbError);

      // Return empty results with CORS headers if database fails
      return res.status(200).json({
        success: true,
        data: {
          predictions: [],
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: 0,
            totalPages: 0,
          },
        },
        message: 'Database temporarily unavailable',
        cors: 'working'
      });
    }

    res.json({
      success: true,
      data: {
        predictions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getPredictionById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const prediction = await prisma.prediction.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
          },
        },
        sportsData: true,
      },
    });

    if (!prediction) {
      return next(createError('Prediction not found', 404));
    }

    // Check if user can access this prediction
    const user = (req as AuthenticatedRequest).user;
    if (!prediction.isPublished && (!user || (user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN' && user.id !== prediction.userId))) {
      return next(createError('Prediction not found', 404));
    }

    res.json({
      success: true,
      data: { prediction },
    });
  } catch (error) {
    next(error);
  }
};

export const generatePredictions = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { matchId, markets = ['moneyline', 'spread', 'total'] } = req.body;

    if (!matchId) {
      return next(createError('Match ID is required', 400));
    }

    // Get match data
    const match = await prisma.sportsData.findUnique({
      where: { id: matchId },
    });

    if (!match) {
      return next(createError('Match not found', 404));
    }

    // Check if predictions already exist for this match
    const existingPredictions = await prisma.prediction.findMany({
      where: {
        sportsDataId: matchId,
        source: 'ML',
      },
    });

    if (existingPredictions.length > 0) {
      return res.json({
        success: true,
        message: 'Predictions already exist for this match',
        data: { predictions: existingPredictions },
      });
    }

    logger.info(`Generating predictions for match ${matchId} by user ${req.user?.email}`);

    // Convert to MatchData format for prediction engine
    const matchData = {
      matchId: match.externalId || match.id,
      sport: match.sport,
      league: match.league,
      homeTeam: match.homeTeam,
      awayTeam: match.awayTeam,
      matchDate: match.matchDate,
      odds: match.odds as any,
      stats: match.stats as any,
      source: match.source,
      status: match.status,
      homeScore: match.homeScore || undefined,
      awayScore: match.awayScore || undefined,
    };

    // Generate predictions
    const predictions = await predictionEngine.generatePredictions(matchData, markets);

    if (predictions.length === 0) {
      return next(createError('No valid predictions could be generated for this match', 400));
    }

    // Save predictions to database
    const savedPredictions = [];
    for (const prediction of predictions) {
      const saved = await prisma.prediction.create({
        data: {
          sport: match.sport,
          league: match.league,
          homeTeam: match.homeTeam,
          awayTeam: match.awayTeam,
          matchDate: match.matchDate,
          market: prediction.market,
          pick: prediction.pick,
          confidence: prediction.confidence,
          odds: prediction.odds,
          // edge: prediction.edge, // Field not in schema
          expectedValue: prediction.expectedValue,
          factors: JSON.stringify(prediction.factors),
          analysis: prediction.analysis,
          source: 'ML',
          isPremium: prediction.confidence >= 75,
          userId: req.user!.id,
          sportsDataId: match.id,
        },
      });
      savedPredictions.push(saved);
    }

    logger.info(`Generated ${savedPredictions.length} predictions for match ${matchId}`);

    res.status(201).json({
      success: true,
      message: 'Predictions generated successfully',
      data: { predictions: savedPredictions },
    });
  } catch (error) {
    next(error);
  }
};

export const createManualPrediction = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const predictionData = req.body;

    // Validate required fields
    const requiredFields = ['sport', 'league', 'homeTeam', 'awayTeam', 'matchDate', 'market', 'pick', 'confidence', 'odds'];
    for (const field of requiredFields) {
      if (!predictionData[field]) {
        return next(createError(`${field} is required`, 400));
      }
    }

    const prediction = await prisma.prediction.create({
      data: {
        ...predictionData,
        matchDate: new Date(predictionData.matchDate),
        source: 'MANUAL',
        userId: req.user!.id,
      },
    });

    res.status(201).json({
      success: true,
      message: 'Manual prediction created successfully',
      data: { prediction },
    });
  } catch (error) {
    next(error);
  }
};

export const updatePrediction = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const existingPrediction = await prisma.prediction.findUnique({
      where: { id },
    });

    if (!existingPrediction) {
      return next(createError('Prediction not found', 404));
    }

    // Check permissions
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'SUPER_ADMIN' && req.user!.id !== existingPrediction.userId) {
      return next(createError('Insufficient permissions', 403));
    }

    const updatedPrediction = await prisma.prediction.update({
      where: { id },
      data: {
        ...updateData,
        ...(updateData.matchDate && { matchDate: new Date(updateData.matchDate) }),
      },
    });

    res.json({
      success: true,
      message: 'Prediction updated successfully',
      data: { prediction: updatedPrediction },
    });
  } catch (error) {
    next(error);
  }
};

export const deletePrediction = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const existingPrediction = await prisma.prediction.findUnique({
      where: { id },
    });

    if (!existingPrediction) {
      return next(createError('Prediction not found', 404));
    }

    // Check permissions
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'SUPER_ADMIN' && req.user!.id !== existingPrediction.userId) {
      return next(createError('Insufficient permissions', 403));
    }

    await prisma.prediction.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: 'Prediction deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

export const publishPrediction = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { isPublished = true } = req.body;

    const prediction = await prisma.prediction.findUnique({
      where: { id },
    });

    if (!prediction) {
      return next(createError('Prediction not found', 404));
    }

    const updatedPrediction = await prisma.prediction.update({
      where: { id },
      data: {
        isPublished,
      },
    });

    res.json({
      success: true,
      message: `Prediction ${isPublished ? 'published' : 'unpublished'} successfully`,
      data: { prediction: updatedPrediction },
    });
  } catch (error) {
    next(error);
  }
};

export const getPredictionStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const [
      totalPredictions,
      publishedPredictions,
      premiumPredictions,
      completedPredictions,
      winningPredictions,
      sportBreakdown,
      recentPredictions,
    ] = await Promise.all([
      prisma.prediction.count(),
      prisma.prediction.count({ where: { isPublished: true } }),
      prisma.prediction.count({ where: { isPremium: true } }),
      prisma.prediction.count({ where: { status: 'COMPLETED' } }),
      prisma.prediction.count({ where: { result: 'WIN' } }),
      prisma.prediction.groupBy({
        by: ['sport'],
        _count: { sport: true },
        orderBy: { _count: { sport: 'desc' } },
      }),
      prisma.prediction.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    const winRate = completedPredictions > 0 ? (winningPredictions / completedPredictions) * 100 : 0;

    res.json({
      success: true,
      data: {
        totalPredictions,
        publishedPredictions,
        premiumPredictions,
        completedPredictions,
        winningPredictions,
        winRate: Math.round(winRate * 100) / 100,
        recentPredictions,
        sportBreakdown: sportBreakdown.map(item => ({
          sport: item.sport,
          count: item._count.sport,
        })),
      },
    });
  } catch (error) {
    next(error);
  }
};
