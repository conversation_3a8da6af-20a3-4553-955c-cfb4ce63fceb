# Vercel Deployment Guide

## Backend Deployment to Vercel

### Prerequisites
1. Install Vercel CLI: `npm i -g vercel`
2. Login to Vercel: `vercel login`

### Step 1: Deploy Backend
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Deploy to Vercel:
   ```bash
   vercel --prod
   ```

3. Follow the prompts:
   - Set up and deploy? **Y**
   - Which scope? Select your account
   - Link to existing project? **N**
   - Project name: `1300blk-backend` (or your preferred name)
   - Directory: `./` (current directory)
   - Override settings? **N**

### Step 2: Configure Environment Variables
After deployment, add these environment variables in Vercel dashboard:

**Required Variables:**
```
DATABASE_URL=your-production-database-url
JWT_SECRET=your-production-jwt-secret-key-make-it-very-secure
NODE_ENV=production
CORS_ORIGIN=https://1300blk.online
```

**Optional API Keys:**
```
ESPN_API_KEY=your-espn-api-key
ODDS_API_KEY=your-odds-api-key
SPORTS_REFERENCE_API_KEY=your-sports-reference-api-key
SPORTS_RADAR_API_KEY=your-sports-radar-api-key
RAPID_API_KEY=your-rapid-api-key
SCRAPERAPI_KEY=your-scraper-api-key
REDIS_URL=your-redis-cloud-url
```

### Step 3: Update Frontend Configuration
1. Get your Vercel backend URL (e.g., `https://1300blk-backend.vercel.app`)
2. Update `.env.production` in the frontend:
   ```
   VITE_API_BASE_URL=https://your-backend-app.vercel.app/api
   ```

### Step 4: Database Setup
For production, you'll need a cloud database:

**Option 1: Vercel Postgres**
1. Go to Vercel dashboard → Storage → Create Database
2. Select Postgres
3. Copy the DATABASE_URL to your environment variables

**Option 2: Railway/PlanetScale/Supabase**
1. Create a PostgreSQL database
2. Update your DATABASE_URL environment variable
3. Run migrations: `npx prisma migrate deploy`

### Step 5: Test Deployment
1. Visit your backend URL: `https://your-backend-app.vercel.app/health`
2. Should return: `{"status":"OK","timestamp":"..."}`

### Step 6: Update Frontend
1. Deploy your frontend with the updated API URL
2. Test the connection between frontend and backend

## Troubleshooting

### Common Issues:
1. **Build Errors**: Check that all dependencies are in `package.json`
2. **Database Connection**: Ensure DATABASE_URL is correct
3. **CORS Errors**: Verify CORS_ORIGIN matches your frontend URL
4. **Function Timeout**: Increase timeout in `vercel.json` if needed

### Logs:
- View logs: `vercel logs your-deployment-url`
- Real-time logs: `vercel logs your-deployment-url --follow`

## Environment Variables Reference

Copy these to your Vercel project settings:

```bash
# Required
DATABASE_URL="postgresql://user:password@host:port/database"
JWT_SECRET="your-super-secure-jwt-secret-key"
NODE_ENV="production"
CORS_ORIGIN="https://1300blk.online"

# Optional
ESPN_API_KEY=""
ODDS_API_KEY=""
SPORTS_REFERENCE_API_KEY=""
SPORTS_RADAR_API_KEY=""
RAPID_API_KEY=""
SCRAPERAPI_KEY=""
REDIS_URL=""
```

## Post-Deployment Checklist
- [ ] Backend health check works
- [ ] Database connection established
- [ ] Frontend can connect to backend
- [ ] Authentication works
- [ ] API endpoints respond correctly
- [ ] CORS configured properly
- [ ] Environment variables set
- [ ] Logs show no errors
