import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'white' | 'dark';
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  variant = 'default', 
  showIcon = true, 
  showText = true,
  className = '' 
}) => {
  const sizeClasses = {
    sm: {
      icon: 'h-6 w-6',
      text: 'text-lg',
      container: 'gap-2'
    },
    md: {
      icon: 'h-8 w-8',
      text: 'text-xl',
      container: 'gap-2'
    },
    lg: {
      icon: 'h-10 w-10',
      text: 'text-2xl',
      container: 'gap-3'
    },
    xl: {
      icon: 'h-12 w-12',
      text: 'text-3xl',
      container: 'gap-3'
    }
  };

  const variantClasses = {
    default: {
      icon: 'text-red-500',
      text: 'text-foreground'
    },
    white: {
      icon: 'text-white',
      text: 'text-white'
    },
    dark: {
      icon: 'text-red-500',
      text: 'text-gray-900'
    }
  };

  const currentSize = sizeClasses[size];
  const currentVariant = variantClasses[variant];

  return (
    <div className={`flex items-center ${currentSize.container} ${className}`}>
      {showIcon && (
        <div className={`${currentSize.icon} ${currentVariant.icon} relative`}>
          <img
            src="/lovable-uploads/d2e31152-4343-4f36-a76d-fd6714c7dc7b.png"
            alt="1300BLK Logo"
            className={`${currentSize.icon} drop-shadow-lg object-contain`}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-red-600 opacity-20 rounded-full blur-sm"></div>
        </div>
      )}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bruno font-bold ${currentSize.text} ${currentVariant.text} leading-none tracking-wider`}>
            1300BLK
          </span>
          <span className={`text-xs ${currentVariant.text} opacity-80 font-medium tracking-widest font-bruno`}>
            SPORTS PREDICTIONS
          </span>
        </div>
      )}
    </div>
  );
};

// Preset logo variants for common use cases
export const LogoHeader: React.FC<{ className?: string }> = ({ className }) => (
  <Logo size="md" variant="default" className={className} />
);

export const LogoFooter: React.FC<{ className?: string }> = ({ className }) => (
  <Logo size="sm" variant="default" className={className} />
);

export const LogoAuth: React.FC<{ className?: string }> = ({ className }) => (
  <Logo size="lg" variant="default" className={className} />
);

export const LogoHero: React.FC<{ className?: string }> = ({ className }) => (
  <Logo size="xl" variant="default" className={className} />
);

export const LogoIcon: React.FC<{ size?: 'sm' | 'md' | 'lg' | 'xl', className?: string }> = ({ 
  size = 'md', 
  className 
}) => (
  <Logo size={size} variant="default" showText={false} className={className} />
);

export default Logo;
