import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { logger } from '@/utils/logger';
import { connectDatabase } from '@/config/database';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import sportsDataRoutes from '@/routes/sportsData';
import predictionRoutes from '@/routes/predictions';
import scrapingRoutes from '@/routes/scraping';
import paymentRoutes from '@/routes/payments';
import automationRoutes from '@/routes/automation';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// CORS configuration
const allowedOrigins = [
  'http://localhost:8080',
  'http://localhost:5173',
  'https://1300blk.online',
  'https://www.1300blk.online',
  'https://onethreezerozeroblk-77-hgy2lop26-bmds-projects-6efc3abf.vercel.app',
  'https://onethreezerozeroblk-77-6hi10d9o3-bmds-projects-6efc3abf.vercel.app',
  'https://onethreezerozeroblk-77-7ea3ribwi-bmds-projects-6efc3abf.vercel.app',
  'https://onethreezerozeroblk-77-7h55fvjdq-bmds-projects-6efc3abf.vercel.app'
];

// Emergency fallback: Allow all 1300blk.online subdomains
const isAllowed1300blkDomain = (origin: string) => {
  return origin && (
    origin === 'https://1300blk.online' ||
    origin === 'https://www.1300blk.online' ||
    origin.endsWith('.1300blk.online')
  );
};

// Add environment-specific origins
if (process.env.CORS_ORIGIN) {
  const envOrigins = process.env.CORS_ORIGIN.split(',').map(origin => origin.trim());
  allowedOrigins.push(...envOrigins);
}

// Log allowed origins for debugging
logger.info('CORS allowed origins:', allowedOrigins);

// Middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(compression());
app.use(limiter);

// CORS middleware - single, comprehensive configuration
app.use(cors({
  origin: (origin, callback) => {
    logger.info(`CORS request from origin: ${origin}`);

    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {
      logger.info('CORS: Allowing request with no origin');
      return callback(null, true);
    }

    // Check if origin is allowed
    if (allowedOrigins.includes(origin) || isAllowed1300blkDomain(origin)) {
      logger.info(`CORS: Allowing origin: ${origin}`);
      return callback(null, true);
    } else {
      logger.warn(`CORS blocked origin: ${origin}. Allowed origins: ${allowedOrigins.join(', ')}`);
      // For debugging, allow all origins temporarily in development
      if (process.env.NODE_ENV === 'development') {
        logger.warn('CORS: Allowing origin in development mode');
        return callback(null, true);
      }
      return callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name'
  ],
  exposedHeaders: ['Content-Length', 'X-Total-Count'],
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 200
}));
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    cors: 'enabled',
    origin: req.headers.origin || 'no-origin'
  });
});

// CORS test endpoint
app.get('/cors-test', (req, res) => {
  res.status(200).json({
    message: 'CORS test successful',
    origin: req.headers.origin,
    timestamp: new Date().toISOString(),
    allowedOrigins: allowedOrigins,
    corsEnabled: true
  });
});

// Simple API test endpoint
app.get('/api/test', (req, res) => {
  res.status(200).json({
    message: 'API is working',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin,
    method: req.method,
    url: req.url
  });
});



// Emergency API test endpoint - bypass database
app.get('/api/test', (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', 'https://www.1300blk.online');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  res.status(200).json({
    message: 'API is working',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin,
    cors: 'enabled',
    environment: process.env.NODE_ENV
  });
});

// Emergency predictions test endpoint - no database
app.get('/api/predictions-test', (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', 'https://www.1300blk.online');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  res.status(200).json({
    message: 'Predictions endpoint test',
    predictions: [
      {
        id: 'test-1',
        sport: 'football',
        homeTeam: 'Test Team A',
        awayTeam: 'Test Team B',
        prediction: 'home_win',
        confidence: 0.75,
        status: 'pending'
      }
    ],
    total: 1,
    cors: 'working'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/sports-data', sportsDataRoutes);
app.use('/api/predictions', predictionRoutes);
app.use('/api/scraping', scrapingRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/automation', automationRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // Connect to database
    await connectDatabase();

    // Initialize automation system
    const { scrapingScheduler } = await import('@/services/automation/scrapingScheduler');
    await scrapingScheduler.initialize();
    logger.info('🤖 Automation system initialized');

    app.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();

export default app;
