# 🗄️ Database Configuration Guide

## **Current Database Setup**

### **Development Environment**
- **Database**: SQLite (file-based)
- **Location**: `backend/dev.db`
- **ORM**: Prisma
- **Connection**: `DATABASE_URL="file:./dev.db"`

### **Production Environment (Recommended)**
- **Database**: PostgreSQL
- **Provider**: Vercel Postgres (recommended)
- **Connection**: `DATABASE_URL="postgresql://user:password@host:port/database"`

## **🚀 Database Options for Production**

### **Option 1: Vercel Postgres (Recommended)**
✅ **Best for Vercel deployments**

1. **Create Database**:
   - Go to Vercel Dashboard → Storage → Create Database
   - Select "Postgres"
   - Choose your region (same as backend deployment)

2. **Get Connection String**:
   - Copy the `DATABASE_URL` from Vercel dashboard
   - Add to environment variables in Vercel

3. **Environment Variable**:
   ```bash
   DATABASE_URL="***************************************/verceldb?sslmode=require"
   ```

### **Option 2: Railway**
✅ **Great performance and pricing**

1. **Setup**:
   - Go to railway.app
   - Create new project → Add PostgreSQL
   - Copy connection string

2. **Environment Variable**:
   ```bash
   DATABASE_URL="****************************************/railway"
   ```

### **Option 3: PlanetScale**
✅ **Serverless MySQL (requires schema changes)**

1. **Setup**:
   - Go to planetscale.com
   - Create database
   - Get connection string

2. **Note**: Requires updating Prisma schema for MySQL compatibility

### **Option 4: Supabase**
✅ **Full-featured with auth and storage**

1. **Setup**:
   - Go to supabase.com
   - Create project
   - Get PostgreSQL connection string

2. **Environment Variable**:
   ```bash
   DATABASE_URL="****************************************/postgres"
   ```

## **🔧 Database Migration Commands**

### **Development**
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Reset database (development only)
npx prisma migrate reset

# View database
npx prisma studio
```

### **Production**
```bash
# Deploy migrations to production
npx prisma migrate deploy

# Generate client for production
npx prisma generate
```

## **📊 Current Database Schema**

The application uses the following main tables:

- **users**: User accounts and authentication
- **profiles**: User profile information
- **predictions**: Sports predictions data
- **live_predictions**: Real-time prediction data
- **sports_data**: Sports events and statistics
- **audit_logs**: System audit trail
- **global_settings**: Application configuration

## **🔐 Environment Variables for Database**

### **Development (.env)**
```bash
DATABASE_URL="file:./dev.db"
```

### **Production (.env.production)**
```bash
# Use your chosen database provider
DATABASE_URL="postgresql://user:password@host:port/database"

# Additional database settings
DB_POOL_SIZE="10"
DB_TIMEOUT="30000"
DB_SSL="true"
```

## **⚡ Performance Recommendations**

1. **Connection Pooling**: Use connection pooling for production
2. **Indexing**: Ensure proper indexes on frequently queried fields
3. **Caching**: Implement Redis for caching frequent queries
4. **Monitoring**: Set up database monitoring and alerts

## **🚨 Security Best Practices**

1. **SSL/TLS**: Always use SSL connections in production
2. **Credentials**: Never commit database credentials to version control
3. **Access Control**: Use least-privilege database users
4. **Backups**: Set up automated database backups
5. **Monitoring**: Monitor for suspicious database activity

## **📝 Next Steps**

1. Choose your preferred database provider
2. Set up the database instance
3. Update environment variables in Vercel dashboard
4. Run database migrations
5. Test the connection

**Recommended**: Start with Vercel Postgres for simplicity and seamless integration with your Vercel backend deployment.
