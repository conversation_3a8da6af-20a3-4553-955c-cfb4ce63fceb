const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up database...');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from .env.example...');
  const examplePath = path.join(__dirname, '..', '.env.example');
  fs.copyFileSync(examplePath, envPath);
  console.log('⚠️  Please update the DATABASE_URL in .env file with your PostgreSQL credentials');
}

try {
  // Generate Prisma client
  console.log('📦 Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

  // Run database migrations
  console.log('🔄 Running database migrations...');
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

  // Seed the database
  console.log('🌱 Seeding database...');
  execSync('npx tsx prisma/seed.ts', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

  console.log('✅ Database setup completed successfully!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Update your .env file with actual API keys');
  console.log('2. Run "npm run dev" to start the development server');
  console.log('3. Access the API at http://localhost:3001');
  console.log('');
  console.log('Default admin credentials:');
  console.log('Email: <EMAIL>');
  console.log('Password: admin123');

} catch (error) {
  console.error('❌ Database setup failed:', error.message);
  console.log('');
  console.log('Make sure PostgreSQL is running and the DATABASE_URL in .env is correct.');
  process.exit(1);
}
