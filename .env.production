# Production Environment
VITE_API_BASE_URL=https://1300blk-backendvercel-endgdp9k3-bmds-projects-6efc3abf.vercel.app/api
VITE_APP_URL=https://1300blk-backendvercel-endgdp9k3-bmds-projects-6efc3abf.vercel.app

# Production Database (Vercel Postgres recommended)
DATABASE_URL="libsql://1300blkai-vercel-icfg-************************.aws-us-east-1.turso.io"

# Authentication (Use strong secrets in production)
JWT_SECRET="your-production-jwt-secret-key-make-it-very-secure"
JWT_EXPIRES_IN="7d"

# Payment Providers (Production Keys)
# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY="pk_live_your_paystack_live_public_key"
PAYSTACK_SECRET_KEY="sk_live_your_paystack_live_secret_key"

# Flutterwave Configuration
VITE_FLUTTERWAVE_PUBLIC_KEY="FLWPUBK-your_flutterwave_live_public_key"
FLUTTERWAVE_SECRET_KEY="FLWSECK-your_flutterwave_live_secret_key"

# Crypto Payment Configuration
VITE_CRYPTO_WALLET_ADDRESS="your_production_crypto_wallet_address"
CRYPTO_API_KEY="your_production_crypto_api_key"

# Sports Data APIs (Production Keys)
ESPN_API_KEY="your_production_espn_api_key"
ODDS_API_KEY="your_production_odds_api_key"
SPORTS_REFERENCE_API_KEY="your_production_sports_reference_api_key"
SPORTS_RADAR_API_KEY="your_production_sports_radar_api_key"
RAPID_API_KEY="your_production_rapid_api_key"

# Web Scraping (Production)
SCRAPERAPI_KEY="your_production_scraperapi_key"
PROXY_URL="your_production_proxy_url"

# Email Configuration (Production)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_production_app_password"

# Redis (Production - use Redis Cloud)
REDIS_URL="redis://your_production_redis_url"

# Application Settings
NODE_ENV="production"
PORT="3000"
CORS_ORIGIN="https://1300blk.online"

# Security (Production)
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

UPSTASH_VECTOR_REST_TOKEN="ABEFMG9uLWd1bGwtMTM3MzItdXMxYWRtaW5ZbVE0TVdZeE5tRXRZV016TVMwME5UZzRMVGcyWTJNdFlUbG1ZV0ZpTjJZeE1qTTU="
UPSTASH_VECTOR_REST_READONLY_TOKEN="ABEIMG9uLWd1bGwtMTM3MzItdXMxcmVhZG9ubHlPVEZtTkdaa056RXRaVGd6WlMwMFpUYzVMV0kzTmpjdE16WXlNekExTWpZNU5HUmg="
UPSTASH_VECTOR_REST_URL="https://on-gull-13732-us1-vector.upstash.io"

TURSO_AUTH_TOKEN="***************************************************************************************************************************************************************************************************************************************************************************"
TURSO_DATABASE_URL="libsql://1300blkai-vercel-icfg-************************.aws-us-east-1.turso.io"


